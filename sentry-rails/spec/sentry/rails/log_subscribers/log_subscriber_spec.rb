# frozen_string_literal: true

require "spec_helper"
require "sentry/rails/log_subscriber"

RSpec.describe Sentry::Rails::LogSubscriber, type: :request do
  # Create a test subscriber that responds to real ActiveSupport notifications
  let(:test_subscriber_class) do
    Class.new(described_class) do
      attach_to :test_component

      def test_event(event)
        log_structured_event(
          message: "Test event occurred",
          level: level_for_duration(duration_ms(event)),
          attributes: {
            duration_ms: duration_ms(event),
            test_data: event.payload[:test_data],
            component: "test_component"
          }
        )
      end

      def slow_test_event(event)
        log_structured_event(
          message: "Slow test event occurred",
          level: level_for_duration(duration_ms(event), 100.0), # Lower threshold for testing
          attributes: {
            duration_ms: duration_ms(event),
            test_data: event.payload[:test_data],
            component: "test_component"
          }
        )
      end
    end
  end

  let(:captured_logs) { [] }
  let(:test_logger) do
    logger = double("test_logger")
    allow(logger).to receive(:trace) { |msg, **attrs| captured_logs << { level: :trace, message: msg, attributes: attrs } }
    allow(logger).to receive(:debug) { |msg, **attrs| captured_logs << { level: :debug, message: msg, attributes: attrs } }
    allow(logger).to receive(:info) { |msg, **attrs| captured_logs << { level: :info, message: msg, attributes: attrs } }
    allow(logger).to receive(:warn) { |msg, **attrs| captured_logs << { level: :warn, message: msg, attributes: attrs } }
    allow(logger).to receive(:error) { |msg, **attrs| captured_logs << { level: :error, message: msg, attributes: attrs } }
    allow(logger).to receive(:fatal) { |msg, **attrs| captured_logs << { level: :fatal, message: msg, attributes: attrs } }
    logger
  end

  before do
    make_basic_app do |config|
      config.enable_logs = true
    end

    # Set up Sentry logger to capture logs
    allow(Sentry).to receive(:logger).and_return(test_logger)

    # Clear any existing subscribers to avoid interference
    captured_logs.clear
  end

  after do
    # Clean up any subscribers we created
    test_subscriber_class.detach_from(:test_component) if defined?(test_subscriber_class)
  end

  describe "ActiveSupport notifications integration" do
    it "responds to real ActiveSupport notifications" do
      # Trigger a real ActiveSupport notification
      ActiveSupport::Notifications.instrument("test_event.test_component", test_data: "sample_data") do
        sleep(0.01) # Small delay to ensure measurable duration
      end

      # Verify that our subscriber captured and logged the event
      expect(captured_logs).not_to be_empty
      log_entry = captured_logs.first

      expect(log_entry[:level]).to eq(:info)
      expect(log_entry[:message]).to eq("Test event occurred")
      expect(log_entry[:attributes][:test_data]).to eq("sample_data")
      expect(log_entry[:attributes][:component]).to eq("test_component")
      expect(log_entry[:attributes][:duration_ms]).to be_a(Float)
      expect(log_entry[:attributes][:duration_ms]).to be > 0
    end

    it "uses appropriate log level based on duration" do
      # Test fast event (should be info level)
      ActiveSupport::Notifications.instrument("test_event.test_component", test_data: "fast") do
        # Very short duration
      end

      # Test slow event with custom threshold (should be warn level)
      ActiveSupport::Notifications.instrument("slow_test_event.test_component", test_data: "slow") do
        sleep(0.15) # Longer than our 100ms threshold
      end

      expect(captured_logs.size).to eq(2)

      fast_log = captured_logs.first
      slow_log = captured_logs.last

      expect(fast_log[:level]).to eq(:info)
      expect(fast_log[:attributes][:test_data]).to eq("fast")

      expect(slow_log[:level]).to eq(:warn)
      expect(slow_log[:attributes][:test_data]).to eq("slow")
      expect(slow_log[:attributes][:duration_ms]).to be > 100
    end

    it "handles events with various payload data" do
      test_payloads = [
        { test_data: "string_value" },
        { test_data: { nested: "hash" } },
        { test_data: [1, 2, 3] },
        { test_data: nil }
      ]

      test_payloads.each_with_index do |payload, index|
        ActiveSupport::Notifications.instrument("test_event.test_component", payload) do
          # Minimal processing
        end
      end

      expect(captured_logs.size).to eq(test_payloads.size)

      captured_logs.each_with_index do |log_entry, index|
        expect(log_entry[:message]).to eq("Test event occurred")
        expect(log_entry[:attributes][:test_data]).to eq(test_payloads[index][:test_data])
      end
    end
  end

  describe "error handling" do
    let(:failing_logger) do
      logger = double("failing_logger")
      allow(logger).to receive(:info).and_raise(StandardError.new("Logging failed"))
      logger
    end

    let(:sdk_logger_output) { StringIO.new }
    let(:sdk_logger) { ::Logger.new(sdk_logger_output) }

    before do
      allow(Sentry).to receive(:logger).and_return(failing_logger)
      allow(Sentry).to receive(:configuration).and_return(double("configuration", sdk_logger: sdk_logger))
    end

    it "handles logging errors gracefully and logs to sdk_logger" do
      expect {
        ActiveSupport::Notifications.instrument("test_event.test_component", test_data: "error_test") do
          # This should trigger our subscriber which will fail to log
        end
      }.not_to raise_error

      # Check that the error was logged to the SDK logger
      sdk_output = sdk_logger_output.string
      expect(sdk_output).to include("Failed to log structured event: Logging failed")
    end
  end

  describe "duration calculation and level determination" do
    it "calculates duration correctly from real events" do
      ActiveSupport::Notifications.instrument("test_event.test_component", test_data: "duration_test") do
        sleep(0.05) # 50ms
      end

      log_entry = captured_logs.first
      duration = log_entry[:attributes][:duration_ms]

      expect(duration).to be_a(Float)
      expect(duration).to be >= 40.0 # Allow some variance
      expect(duration).to be < 100.0 # Should be less than 100ms
      expect(duration.round(2)).to eq(duration) # Should be rounded to 2 decimal places
    end

    it "determines log level based on duration thresholds" do
      # Test with default threshold (1000ms) - should be info
      ActiveSupport::Notifications.instrument("test_event.test_component", test_data: "fast") do
        sleep(0.01) # Very fast
      end

      # Test with custom threshold (100ms) - should be warn for slow events
      ActiveSupport::Notifications.instrument("slow_test_event.test_component", test_data: "slow") do
        sleep(0.12) # Slower than 100ms threshold
      end

      expect(captured_logs.size).to eq(2)

      fast_log = captured_logs.first
      slow_log = captured_logs.last

      expect(fast_log[:level]).to eq(:info) # Fast event with default threshold
      expect(slow_log[:level]).to eq(:warn) # Slow event with custom threshold
    end
  end

  describe "Rails version compatibility" do
    context "when Rails version is less than 6.0", skip: Rails.version.to_f >= 6.0 ? "Rails 6.0+" : false do
      it "provides custom detach_from implementation" do
        # Create a temporary subscriber to test detachment
        temp_subscriber_class = Class.new(described_class) do
          attach_to :temp_test

          def temp_event(event)
            log_structured_event(message: "Temp event", attributes: { data: event.payload[:data] })
          end
        end

        # Verify subscriber is attached
        ActiveSupport::Notifications.instrument("temp_event.temp_test", data: "before_detach") do
          # Event processing
        end

        initial_log_count = captured_logs.size

        # Detach the subscriber
        temp_subscriber_class.detach_from(:temp_test)

        # Verify subscriber is detached - no new logs should be created
        ActiveSupport::Notifications.instrument("temp_event.temp_test", data: "after_detach") do
          # Event processing
        end

        expect(captured_logs.size).to eq(initial_log_count) # No new logs after detachment
      end
    end

    context "when Rails version is 6.0 or higher", skip: Rails.version.to_f < 6.0 ? "Rails 5.x" : false do
      it "uses Rails built-in detach_from method" do
        expect(described_class).to respond_to(:detach_from)

        # Test that detach_from works (Rails built-in implementation)
        temp_subscriber_class = Class.new(described_class) do
          attach_to :temp_test_rails6

          def temp_event(event)
            log_structured_event(message: "Temp event Rails 6+", attributes: { data: event.payload[:data] })
          end
        end

        # Verify subscriber works
        ActiveSupport::Notifications.instrument("temp_event.temp_test_rails6", data: "test") do
          # Event processing
        end

        initial_log_count = captured_logs.size

        # Detach using Rails built-in method
        temp_subscriber_class.detach_from(:temp_test_rails6)

        # Verify detachment worked
        ActiveSupport::Notifications.instrument("temp_event.temp_test_rails6", data: "after_detach") do
          # Event processing
        end

        expect(captured_logs.size).to eq(initial_log_count) # No new logs after detachment
      end
    end
  end
end
end
