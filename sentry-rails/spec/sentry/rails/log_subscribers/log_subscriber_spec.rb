# frozen_string_literal: true

require "spec_helper"
require "sentry/rails/log_subscriber"

RSpec.describe Sentry::Rails::LogSubscriber, type: :request do
  # Create a test subscriber that responds to real ActiveSupport notifications
  let(:test_subscriber_class) do
    Class.new(described_class) do
      attach_to :test_component

      def test_event(event)
        log_structured_event(
          message: "Test event occurred",
          attributes: {
            duration_ms: duration_ms(event),
            test_data: event.payload[:test_data],
            component: "test_component"
          }
        )
      end

      def error_test_event(event)
        log_structured_event(
          message: "Error test event",
          level: :error,
          attributes: {
            duration_ms: duration_ms(event),
            error_data: event.payload[:error_data]
          }
        )
      end
    end
  end

  before do
    make_basic_app do |config|
      config.enable_logs = true
    end

    @original_logger = Sentry.logger
    @debug_logger = Sentry::DebugStructuredLogger.new(Sentry.configuration)
    allow(Sentry).to receive(:logger).and_return(@debug_logger)

    # Clear any previous log events
    @debug_logger.clear

    # Ensure the test subscriber is instantiated and attached
    @test_subscriber = test_subscriber_class.new
  end

  after do
    # Clean up any subscribers we created
    test_subscriber_class.detach_from(:test_component) if defined?(test_subscriber_class)
    @debug_logger.clear

    # Restore original logger
    allow(Sentry).to receive(:logger).and_return(@original_logger)
  end

  describe "ActiveSupport notifications integration" do
    it "responds to real ActiveSupport notifications and logs structured events" do
      # Trigger a real ActiveSupport notification
      ActiveSupport::Notifications.instrument("test_event.test_component", test_data: "sample_data") do
        sleep(0.01) # Small delay to ensure measurable duration
      end

      # Get the logged events from DebugStructuredLogger
      logged_events = @debug_logger.logged_events
      expect(logged_events).not_to be_empty

      log_event = logged_events.first
      expect(log_event["level"]).to eq("info")
      expect(log_event["message"]).to eq("Test event occurred")
      expect(log_event["attributes"]["test_data"]).to eq("sample_data")
      expect(log_event["attributes"]["component"]).to eq("test_component")
      expect(log_event["attributes"]["duration_ms"]).to be_a(Float)
      expect(log_event["attributes"]["duration_ms"]).to be > 0
      expect(log_event["timestamp"]).to be_a(String)
    end

    it "uses appropriate log level based on duration thresholds" do
      # Test fast event (should be info level with default 1000ms threshold)
      ActiveSupport::Notifications.instrument("test_event.test_component", test_data: "fast") do
        sleep(0.01) # Very short duration
      end

      logged_events = @debug_logger.logged_events
      expect(logged_events.size).to eq(2)

      fast_log = logged_events.first
      slow_log = logged_events.last

      expect(fast_log["level"]).to eq("info")
      expect(fast_log["attributes"]["test_data"]).to eq("fast")
      expect(fast_log["attributes"]["duration_ms"]).to be < 100

      expect(slow_log["level"]).to eq("warn")
      expect(slow_log["attributes"]["test_data"]).to eq("slow")
      expect(slow_log["attributes"]["duration_ms"]).to be > 100
    end

    it "handles events with various payload data types" do
      test_payloads = [
        { test_data: "string_value" },
        { test_data: { nested: "hash" } },
        { test_data: [1, 2, 3] },
        { test_data: nil }
      ]

      # Expected values after JSON serialization (symbols become strings)
      expected_values = [
        "string_value",
        { "nested" => "hash" },
        [1, 2, 3],
        nil
      ]

      test_payloads.each do |payload|
        ActiveSupport::Notifications.instrument("test_event.test_component", payload) do
          # Minimal processing
        end
      end

      logged_events = @debug_logger.logged_events
      expect(logged_events.size).to eq(test_payloads.size)

      logged_events.each_with_index do |log_event, index|
        expect(log_event["message"]).to eq("Test event occurred")
        expect(log_event["attributes"]["test_data"]).to eq(expected_values[index])
        expect(log_event["level"]).to eq("info")
      end
    end

    it "calculates duration correctly from real events" do
      ActiveSupport::Notifications.instrument("test_event.test_component", test_data: "duration_test") do
        sleep(0.05) # 50ms
      end

      logged_events = @debug_logger.logged_events
      log_event = logged_events.first
      duration = log_event["attributes"]["duration_ms"]

      expect(duration).to be_a(Float)
      expect(duration).to be >= 40.0 # Allow some variance
      expect(duration).to be < 100.0 # Should be less than 100ms
      expect(duration.round(2)).to eq(duration) # Should be rounded to 2 decimal places
    end
  end

  describe "error handling" do
    it "handles logging errors gracefully and logs to sdk_logger" do
      # Create a failing logger that will raise an error
      failing_logger = double("failing_logger")
      allow(failing_logger).to receive(:error).and_raise(StandardError.new("Logging failed"))

      # Capture SDK logger output
      sdk_logger_output = StringIO.new
      sdk_logger = ::Logger.new(sdk_logger_output)

      # Temporarily replace Sentry logger and configuration
      original_logger = Sentry.logger
      original_config = Sentry.configuration

      begin
        allow(Sentry).to receive(:logger).and_return(failing_logger)
        allow(Sentry).to receive(:configuration).and_return(double("configuration", sdk_logger: sdk_logger))

        expect {
          ActiveSupport::Notifications.instrument("error_test_event.test_component", error_data: "error_test") do
            # This should trigger our subscriber which will fail to log
          end
        }.not_to raise_error

        # Check that the error was logged to the SDK logger
        sdk_output = sdk_logger_output.string
        expect(sdk_output).to include("Failed to log structured event: Logging failed")
      ensure
        # Restore original logger and configuration
        allow(Sentry).to receive(:logger).and_return(original_logger)
        allow(Sentry).to receive(:configuration).and_return(original_config)
      end
    end
  end

  describe "Rails version compatibility" do
    context "when Rails version is less than 6.0", skip: Rails.version.to_f >= 6.0 ? "Rails 6.0+" : false do
      it "provides custom detach_from implementation" do
        # Create a temporary subscriber to test detachment
        temp_subscriber_class = Class.new(described_class) do
          attach_to :temp_test

          def temp_event(event)
            log_structured_event(message: "Temp event", attributes: { data: event.payload[:data] })
          end
        end

        # Verify subscriber is attached by triggering an event
        ActiveSupport::Notifications.instrument("temp_event.temp_test", data: "before_detach") do
          # Event processing
        end

        initial_log_count = @debug_logger.logged_events.size
        expect(initial_log_count).to be > 0

        # Detach the subscriber
        temp_subscriber_class.detach_from(:temp_test)

        # Clear existing logs to test detachment
        @debug_logger.clear

        # Verify subscriber is detached - no new logs should be created
        ActiveSupport::Notifications.instrument("temp_event.temp_test", data: "after_detach") do
          # Event processing
        end

        expect(@debug_logger.logged_events).to be_empty # No new logs after detachment
      end
    end

    context "when Rails version is 6.0 or higher", skip: Rails.version.to_f < 6.0 ? "Rails 5.x" : false do
      it "uses Rails built-in detach_from method" do
        expect(described_class).to respond_to(:detach_from)

        # Test that detach_from works (Rails built-in implementation)
        temp_subscriber_class = Class.new(described_class) do
          attach_to :temp_test_rails6

          def temp_event(event)
            log_structured_event(message: "Temp event Rails 6+", attributes: { data: event.payload[:data] })
          end
        end

        # Verify subscriber works
        ActiveSupport::Notifications.instrument("temp_event.temp_test_rails6", data: "test") do
          # Event processing
        end

        initial_log_count = @debug_logger.logged_events.size
        expect(initial_log_count).to be > 0

        # Detach using Rails built-in method
        temp_subscriber_class.detach_from(:temp_test_rails6)

        # Clear existing logs to test detachment
        @debug_logger.clear

        # Verify detachment worked
        ActiveSupport::Notifications.instrument("temp_event.temp_test_rails6", data: "after_detach") do
          # Event processing
        end

        expect(@debug_logger.logged_events).to be_empty # No new logs after detachment
      end
    end
  end
end
