# frozen_string_literal: true

require "spec_helper"
require "sentry/rails/log_subscriber"

RSpec.describe Sentry::Rails::LogSubscriber, type: :request do
  let(:test_subscriber_class) do
    Class.new(described_class) do
      def test_event(event)
        log_structured_event(
          message: "Test event occurred",
          level: :info,
          attributes: {
            duration_ms: duration_ms(event),
            test_data: event.payload[:test_data]
          }
        )
      end
    end
  end

  let(:test_subscriber) { test_subscriber_class.new }
  let(:mock_event) do
    double("ActiveSupport::Notifications::Event",
           duration: 123.456789,
           payload: { test_data: "sample_data" })
  end

  before do
    make_basic_app do |config|
      config.enable_logs = true
    end
  end

  describe "inheritance" do
    it "inherits from ActiveSupport::LogSubscriber" do
      expect(described_class.superclass).to eq(ActiveSupport::LogSubscriber)
    end
  end

  describe "class methods" do
    context "when Rails version is less than 6.0", skip: Rails.version.to_f >= 6.0 ? "Rails 6.0+" : false do
      describe ".detach_from" do
        let(:mock_notifications) { double("ActiveSupport::Notifications") }
        let(:mock_notifier) { double("notifier") }
        let(:mock_listener) { double("listener") }

        before do
          allow(mock_notifications).to receive(:notifier).and_return(mock_notifier)
          allow(mock_notifier).to receive(:listeners_for).and_return([mock_listener])
          allow(mock_listener).to receive(:instance_variable_get).with(:@delegate).and_return(test_subscriber_class.new)
          allow(mock_notifier).to receive(:unsubscribe).with(mock_listener)
        end

        it "unsubscribes listeners for the given namespace" do
          expect(mock_notifier).to receive(:unsubscribe).with(mock_listener)
          test_subscriber_class.detach_from(:test_namespace, mock_notifications)
        end
      end
    end

    context "when Rails version is 6.0 or higher", skip: Rails.version.to_f < 6.0 ? "Rails 5.x" : false do
      it "uses Rails built-in detach_from method" do
        expect(described_class).to respond_to(:detach_from)
      end
    end
  end

  describe "#log_structured_event" do
    let(:mock_logger) { double("logger") }

    before do
      allow(Sentry).to receive(:logger).and_return(mock_logger)
    end

    it "logs with default info level" do
      expect(mock_logger).to receive(:info).with("Test message", foo: "bar")

      test_subscriber.send(:log_structured_event,
                          message: "Test message",
                          attributes: { foo: "bar" })
    end

    it "logs with specified level" do
      expect(mock_logger).to receive(:warn).with("Warning message", error: "details")

      test_subscriber.send(:log_structured_event,
                          message: "Warning message",
                          level: :warn,
                          attributes: { error: "details" })
    end

    it "logs with empty attributes when none provided" do
      expect(mock_logger).to receive(:debug).with("Debug message")

      test_subscriber.send(:log_structured_event,
                          message: "Debug message",
                          level: :debug)
    end

    it "supports all log levels" do
      %i[trace debug info warn error fatal].each do |level|
        expect(mock_logger).to receive(level).with("#{level} message")
        test_subscriber.send(:log_structured_event,
                            message: "#{level} message",
                            level: level)
      end
    end

    context "when logging fails" do
      let(:mock_sdk_logger) { double("sdk_logger") }
      let(:mock_configuration) { double("configuration", sdk_logger: mock_sdk_logger) }

      before do
        allow(Sentry).to receive(:configuration).and_return(mock_configuration)
        allow(mock_logger).to receive(:info).and_raise(StandardError.new("Logging failed"))
      end

      it "handles errors gracefully and logs to sdk_logger" do
        expect(mock_sdk_logger).to receive(:debug).with("Failed to log structured event: Logging failed")

        expect {
          test_subscriber.send(:log_structured_event, message: "Test message")
        }.not_to raise_error
      end
    end
  end

  describe "#duration_ms" do
    it "returns duration rounded to 2 decimal places" do
      result = test_subscriber.send(:duration_ms, mock_event)
      expect(result).to eq(123.46)
    end

    it "handles integer durations" do
      event = double("event", duration: 100)
      result = test_subscriber.send(:duration_ms, event)
      expect(result).to eq(100.0)
    end

    it "handles very small durations" do
      event = double("event", duration: 0.123456)
      result = test_subscriber.send(:duration_ms, event)
      expect(result).to eq(0.12)
    end

    it "handles zero duration" do
      event = double("event", duration: 0.0)
      result = test_subscriber.send(:duration_ms, event)
      expect(result).to eq(0.0)
    end
  end

  describe "#level_for_duration" do
    it "returns :info for durations below default threshold" do
      result = test_subscriber.send(:level_for_duration, 999.9)
      expect(result).to eq(:info)
    end

    it "returns :warn for durations above default threshold" do
      result = test_subscriber.send(:level_for_duration, 1000.1)
      expect(result).to eq(:warn)
    end

    it "returns :info for durations exactly at default threshold" do
      result = test_subscriber.send(:level_for_duration, 1000.0)
      expect(result).to eq(:info)
    end

    it "accepts custom threshold" do
      result = test_subscriber.send(:level_for_duration, 500.0, 400.0)
      expect(result).to eq(:warn)

      result = test_subscriber.send(:level_for_duration, 300.0, 400.0)
      expect(result).to eq(:info)
    end

    it "handles zero duration with custom threshold" do
      result = test_subscriber.send(:level_for_duration, 0.0, 100.0)
      expect(result).to eq(:info)
    end

    it "handles very large durations" do
      result = test_subscriber.send(:level_for_duration, 999999.0)
      expect(result).to eq(:warn)
    end
  end

  describe "integration test" do
    let(:string_io) { StringIO.new }
    let(:logger) { ::Logger.new(string_io) }

    before do
      make_basic_app do |config|
        config.enable_logs = true
        config.logger = logger
      end
    end

    it "logs structured events through the complete flow" do
      test_subscriber.send(:test_event, mock_event)

      # The actual logging behavior depends on Sentry's logger implementation
      # This test ensures no errors are raised during the complete flow
      expect { test_subscriber.send(:test_event, mock_event) }.not_to raise_error
    end
  end
end
